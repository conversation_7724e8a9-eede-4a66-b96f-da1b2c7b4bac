# ================================
# 闲鱼自动回复系统 - Python依赖包
# ================================

# 核心Web框架
fastapi>=0.111.0
uvicorn[standard]>=0.29.0
pydantic>=2.7.0

# 日志记录
loguru>=0.7.0

# 网络通信
websockets>=10.0,<13.0
aiohttp>=3.9.0
requests>=2.31.0
httpx>=0.25.0

# 配置文件处理
PyYAML>=6.0.0
python-dotenv>=1.0.1

# JavaScript执行引擎
PyExecJS>=1.5.1

# 协议缓冲区解析
blackboxprotobuf>=1.0.1

# 系统监控
psutil>=5.9.0

# 文件上传支持
python-multipart>=0.0.6

# AI回复引擎
openai>=1.65.5

# 图像处理（验证码生成、二维码生成）
Pillow>=10.0.0
qrcode[pil]>=7.4.2

# 浏览器自动化（商品搜索、订单详情获取）
playwright>=1.40.0

# 加密和安全
PyJWT>=2.8.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0

# 时间处理
python-dateutil>=2.8.2

# 正则表达式增强
regex>=2023.10.3

# Excel文件处理（数据导入导出）
pandas>=2.0.0
openpyxl>=3.1.0

# 邮件发送（用户注册验证）
email-validator>=2.0.0

# 其他工具库
typing-extensions>=4.7.0

# 注意：
# - sqlite3 是Python内置模块，无需安装
# - smtplib 是Python内置模块，无需安装
# - email 是Python内置模块，无需安装